package com.example.myapplication.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExtendedFloatingActionButton
import androidx.compose.material3.FilledTonalButton
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.myapplication.data.ClinicalPracticeGuidelines
import com.example.myapplication.data.model.ActiveNonComplianceEvent
import com.example.myapplication.data.model.Annotation
import com.example.myapplication.data.model.AnnotationType
import com.example.myapplication.data.model.MannequinConfig
import com.example.myapplication.data.model.NonComplianceEvent
import com.example.myapplication.data.model.ScenarioThresholds
import com.example.myapplication.data.model.VitalSign
import com.example.myapplication.data.model.MonitorReadingStatus
import com.example.myapplication.data.repository.VitalSignsRepository
import com.example.myapplication.data.repository.VitalSignsRepository.ConnectionStatus
import com.example.myapplication.ui.components.ActiveNonComplianceIndicator
import com.example.myapplication.ui.components.AnnotationInput
import com.example.myapplication.ui.components.AnnotationsList
import com.example.myapplication.ui.components.ConnectionStatusIndicator
import com.example.myapplication.ui.components.MannequinMonitorCard
import com.example.myapplication.ui.components.MannequinScenarioItem
import com.example.myapplication.ui.components.NonComplianceTimeline
import com.example.myapplication.ui.theme.Danger
import android.content.Context
import android.widget.Toast
import androidx.compose.ui.platform.LocalContext
import java.util.Date
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.OutlinedButton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*
import androidx.lifecycle.viewModelScope
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.MenuDefaults
import androidx.compose.runtime.produceState
import androidx.compose.material3.RadioButton
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.filled.Delete
import com.google.gson.reflect.TypeToken
import com.google.gson.Gson
import android.util.Log
import kotlinx.coroutines.flow.first
import androidx.compose.material3.TopAppBar
import androidx.compose.material.icons.filled.ArrowDropDown
import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import com.example.myapplication.data.repository.LiveSessionMannequinConfig
import com.example.myapplication.data.Constants
import com.example.myapplication.ui.viewmodel.SessionMetadata

// Define Monitoring Mode Enum
enum class MonitoringMode {
    SIMULATION, LIVE_ZOLL
}

// Data class for Live Monitor Config
// data class LiveMonitorConfig(
//     val mannequinName: String,
//     val ipAddress: String
//     // Add port if needed later
// )

/**
 * Real-time monitor screen with simulation selection and multi-mannequin monitoring
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RealTimeMonitorScreen(
    navController: NavController,
    sectionTitle: String,
    mannequinRepository: VitalSignsRepository
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // Session state
    var isMonitoring by remember { mutableStateOf(false) }
    var sessionStartTime by remember { mutableStateOf<Date?>(null) }
    var simulationType by remember { mutableStateOf("Training") }

    // NEW: Monitoring Mode and Settings State
    var monitoringMode by remember { mutableStateOf(MonitoringMode.SIMULATION) } // Default to Simulation
    var liveMonitorConfigs by remember { mutableStateOf<List<LiveSessionMannequinConfig>>(emptyList()) } // UPDATED TYPE: Load this from persistence
    var showSettingsDialog by remember { mutableStateOf(false) }

    // Mannequin and scenario selection
    val availableMannequins = remember { listOf("Dave", "Chuck", "Freddy", "Oscar", "Matt") }
    val availableScenarios = remember { ScenarioThresholds.getAllScenarios() }

    var selectedMannequins by remember { mutableStateOf(listOf<String>()) }
    var mannequinScenarios by remember { mutableStateOf<Map<String, String>>(mapOf()) }

    // Dialog states
    var showAddMannequinDialog by remember { mutableStateOf(false) }
    var showSaveDialog by remember { mutableStateOf(false) }
    var showAnnotationsDialog by remember { mutableStateOf(false) }

    // UI-related states
    var showSimulationTypeDropdown by remember { mutableStateOf(false) }
    val simulationTypes = listOf("Training", "Graded")

    // Add this right after the coroutine scope declaration
    val latestVitalSignsByMannequin by mannequinRepository.latestVitalSignsByMannequin.collectAsState(initial = emptyMap())
    val connectionStatuses by mannequinRepository.connectionStatuses.collectAsState(initial = emptyMap())

    // Non-compliance and annotation states
    val activeNonComplianceEvents by mannequinRepository.activeNonComplianceEvents.collectAsState(initial = emptyMap())
    val nonComplianceEvents by mannequinRepository.nonComplianceEvents.collectAsState(initial = emptyList())
    val annotations by mannequinRepository.annotations.collectAsState(initial = emptyList())

    // State to track initial settings load
    var initialSettingsLoaded by remember { mutableStateOf(false) }

    // --- Runtime Permissions Handling ---
    val permissionsToRequest = remember {
        mutableListOf(
            Manifest.permission.ACCESS_FINE_LOCATION
        ).apply {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                add(Manifest.permission.BLUETOOTH_SCAN)
                add(Manifest.permission.BLUETOOTH_CONNECT)
            }
            // Add other permissions if ZOXSeries.getBluetoothDeviceBrowser() requires them explicitly
            // and if they are dangerous (e.g. COARSE_LOCATION if FINE is not enough for some reason)
        }.toTypedArray()
    }

    var allPermissionsGranted by remember { mutableStateOf(false) } // Track if all needed permissions are granted

    val multiplePermissionsLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissionsResult ->
        allPermissionsGranted = permissionsResult.values.all { it }
        if (allPermissionsGranted) {
            Toast.makeText(context, "Permissions granted! You can start live monitoring.", Toast.LENGTH_SHORT).show()
            // If the user intended to start monitoring, and it's LIVE_ZOLL, we might trigger it here
            // This depends on how startMonitoring() function is structured
            if (monitoringMode == MonitoringMode.LIVE_ZOLL /* && some_flag_indicating_user_clicked_start */) {
                 // Potentially re-trigger or allow the startMonitoring() function to proceed
            }
        } else {
            Toast.makeText(context, "Required permissions denied. Live monitoring disabled.", Toast.LENGTH_LONG).show()
        }
    }

    // Function to check and request permissions if needed
    fun checkAndRequestPermissions(onPermissionsGranted: () -> Unit) {
        allPermissionsGranted = permissionsToRequest.all {
            ContextCompat.checkSelfPermission(context, it) == PackageManager.PERMISSION_GRANTED
        }
        if (allPermissionsGranted) {
            onPermissionsGranted()
        } else {
            multiplePermissionsLauncher.launch(permissionsToRequest)
        }
    }
    // --- End Runtime Permissions Handling ---

    // Load initial settings once
    LaunchedEffect(Unit) {
        if (!initialSettingsLoaded) { // Prevent reloading if already loaded
            try {
                val mode = mannequinRepository.getMonitoringMode().first()
                val configs = mannequinRepository.getLiveMonitorConfigs().first() // This now returns List<LiveSessionMannequinConfig>
                monitoringMode = mode
                liveMonitorConfigs = configs // Assign directly
                Log.d("RealTimeMonitorScreen", "Loaded settings - Mode: $mode, Configs: ${configs.size}")
            } catch (e: Exception) {
                Log.e("RealTimeMonitorScreen", "Error loading settings", e)
                // Keep defaults if loading fails
                Toast.makeText(context, "Error loading settings, using defaults.", Toast.LENGTH_LONG).show()
            } finally {
                initialSettingsLoaded = true
            }
        }
    }

    // Function to start monitoring
    fun startMonitoring() {
        var canStart = false
        if (monitoringMode == MonitoringMode.SIMULATION) {
            if (selectedMannequins.isNullOrEmpty()) {
                Toast.makeText(context, "Please add mannequins for simulation mode.", Toast.LENGTH_SHORT).show()
            } else {
                canStart = true
            }
        } else { // LIVE_ZOLL Mode
            // For LIVE_ZOLL, permission check is the first gate
            checkAndRequestPermissions {
                // This block executes if permissions are already granted or become granted after request
                if (liveMonitorConfigs.isNullOrEmpty() && monitoringMode == MonitoringMode.LIVE_ZOLL) { // Check again after permission grant
                    // Still prompt to configure, even if permissions are granted, if no configs exist.
                    // However, with SDK discovery, explicit configs are less critical for *starting* discovery.
                    // This toast might be less relevant if SDK auto-discovers.
                    // Toast.makeText(context, "Permissions granted. Please configure live monitors in settings if needed.", Toast.LENGTH_LONG).show()
                    // For now, let's assume SDK discovery will work without pre-configs.
                    Log.d("RealTimeMonitorScreen", "LIVE_ZOLL: Permissions granted. Proceeding to start repository monitoring.")
                     canStart = true // Mark as canStart if permissions are granted for LIVE_ZOLL
                     // Proceed with starting logic specific to LIVE_ZOLL if canStart is true
                     if (canStart) {
                        sessionStartTime = Date()
                        isMonitoring = true
                        mannequinRepository.startMonitoring(
                            mode = MonitoringMode.LIVE_ZOLL,
                            liveConfigs = liveMonitorConfigs, // Still pass for potential name mapping - TYPE IS NOW List<LiveSessionMannequinConfig>
                            simulationType = simulationType
                        )
                        Toast.makeText(context, "Starting Live Zoll SDK Monitoring ($simulationType)...", Toast.LENGTH_SHORT).show()
                    }
            } else {
                    canStart = true // Permissions granted, and configs might exist or not be strictly needed for discovery
                     // Proceed with starting logic specific to LIVE_ZOLL if canStart is true
                     if (canStart) {
                        sessionStartTime = Date()
                        isMonitoring = true
                        mannequinRepository.startMonitoring(
                            mode = MonitoringMode.LIVE_ZOLL,
                            liveConfigs = liveMonitorConfigs, // Still pass for potential name mapping - TYPE IS NOW List<LiveSessionMannequinConfig>
                            simulationType = simulationType
                        )
                        Toast.makeText(context, "Starting Live Zoll SDK Monitoring ($simulationType)...", Toast.LENGTH_SHORT).show()
            }
        }
            } // end of checkAndRequestPermissions lambda
            // Note: canStart for LIVE_ZOLL is now effectively handled asynchronously by the permission callback.
            // The direct flow to start monitoring for LIVE_ZOLL needs to happen *inside* the onPermissionsGranted lambda.
            // So, we don't rely on canStart being true immediately after checkAndRequestPermissions for LIVE_ZOLL.
            return // Exit here for LIVE_ZOLL as start is handled in permission callback
        } // end LIVE_ZOLL mode

        // Proceed only if configuration is valid for SIMULATION or if LIVE_ZOLL started via callback
        if (!canStart) {
             isMonitoring = false
             return
        }

        sessionStartTime = Date()
        isMonitoring = true

        if (monitoringMode == MonitoringMode.SIMULATION) {
                mannequinRepository.startMonitoring(
                    mode = MonitoringMode.SIMULATION,
                    simulationMannequins = selectedMannequins,
                    simulationScenarioMap = mannequinScenarios,
                    simulationType = simulationType
                )
            Toast.makeText(context, "Starting Simulation ($simulationType) for ${selectedMannequins.size} mannequins", Toast.LENGTH_SHORT).show()
        }
        // LIVE_ZOLL case is now handled within the permissions callback
    }

    // Function to stop monitoring
    fun stopMonitoring() {
        Log.d("RealTimeMonitorScreen", "stopMonitoring() called")
        // First update UI state immediately to prevent multiple clicks
        isMonitoring = false

        // Then stop monitoring in the background
        scope.launch {
            try {
                Log.d("RealTimeMonitorScreen", "Stopping monitoring in repository...")
                // We don't need to manually clear active non-compliance events
                // The repository will handle this in stopMonitoring()

                // Now stop monitoring
                withContext(Dispatchers.IO) {
                    mannequinRepository.stopMonitoring()
                }

                Log.d("RealTimeMonitorScreen", "Monitoring stopped, showing save dialog")
                // Show save dialog after successful stop
                showSaveDialog = true
            } catch (e: Exception) {
                Log.e("RealTimeMonitorScreen", "Error stopping monitoring", e)
                Toast.makeText(context, "Error stopping monitoring: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    // ----- UI Definition starts here -----
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
             TopAppBar(title = { Text(sectionTitle) }) // Simple TopAppBar
        },
        floatingActionButton = {
            // Show FAB only when not monitoring and in SIMULATION mode
            if (!isMonitoring && monitoringMode == MonitoringMode.SIMULATION) {
                FloatingActionButton(
                    onClick = { showAddMannequinDialog = true },
                    containerColor = MaterialTheme.colorScheme.tertiary,
                    modifier = Modifier
                        .padding(bottom = 16.dp) // Reduced padding to avoid overlap with bottom nav
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Add Mannequin"
                    )
                }
            }
        },
        // Add bottom padding to ensure content doesn't overlap with navigation bar
        contentWindowInsets = WindowInsets(0, 0, 0, 0)
    ) { paddingValues ->

        // ---- Content based on Loading State ----
        if (!initialSettingsLoaded) {
            // Loading Indicator
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .navigationBarsPadding(), // Add navigation bar padding
                contentAlignment = Alignment.Center
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    CircularProgressIndicator()
                    Spacer(modifier = Modifier.height(8.dp))
                    Text("Loading Settings...")
                }
            }
        } else {
            // ---- Actual Screen Content when Loaded ----
            Column(
                modifier = Modifier
                    .padding(paddingValues)
                    .padding(horizontal = 16.dp, vertical = 12.dp)
                    .fillMaxSize()
                    .navigationBarsPadding() // Add navigation bar padding
                    .verticalScroll(rememberScrollState()) // Make the entire screen scrollable
            ) {

                // --- Configuration Card with professional medical styling ---
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 20.dp), // Increased bottom padding for better spacing
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.9f) // Slightly more transparent for a softer look
                    ),
                    shape = RoundedCornerShape(16.dp), // More rounded corners for a modern medical look
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 2.dp // Subtle elevation
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .padding(horizontal = 20.dp, vertical = 16.dp) // Increased padding for better spacing
                            .fillMaxWidth()
                    ) {
                        // Title with icon for a more professional look
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(bottom = 8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Settings,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(28.dp)
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                text = "Monitoring Configuration",
                                style = MaterialTheme.typography.titleLarge,
                                color = MaterialTheme.colorScheme.onPrimaryContainer,
                                fontWeight = FontWeight.Bold
                            )
                        }

                        // Divider for visual separation
                        HorizontalDivider(
                            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.1f),
                            thickness = 1.dp,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )

                        // Session Type with improved styling
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            // Label with improved styling
                            Text(
                                text = "Session Type:",
                                style = MaterialTheme.typography.titleMedium,
                                modifier = Modifier.width(130.dp), // Slightly wider for better alignment
                                color = MaterialTheme.colorScheme.onPrimaryContainer,
                                fontWeight = FontWeight.Medium // Medium weight for better readability
                            )

                            // Dropdown field with improved styling
                            Box(
                                modifier = Modifier.weight(1f)
                            ) {
                                OutlinedTextField(
                                    value = simulationType,
                                    onValueChange = {},
                                    readOnly = true,
                                    enabled = !isMonitoring,
                                    modifier = Modifier.width(200.dp), // Wider for better appearance
                                    singleLine = true,
                                    shape = RoundedCornerShape(8.dp), // Consistent rounded corners
                                    colors = OutlinedTextFieldDefaults.colors(
                                        unfocusedContainerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.7f),
                                        unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                                        disabledContainerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.5f),
                                        disabledBorderColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f),
                                        disabledTextColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                                    ),
                                    textStyle = MaterialTheme.typography.bodyMedium.copy(
                                        fontWeight = FontWeight.Medium
                                    ),
                                    trailingIcon = {
                                        IconButton(
                                            onClick = { showSimulationTypeDropdown = true },
                                            enabled = !isMonitoring
                                        ) {
                                            Icon(
                                                Icons.Default.ArrowDropDown,
                                                "Select session type",
                                                tint = if (!isMonitoring)
                                                    MaterialTheme.colorScheme.primary
                                                else
                                                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                                            )
                                        }
                                    }
                                )

                                // Dropdown menu with improved styling
                                DropdownMenu(
                                    expanded = showSimulationTypeDropdown,
                                    onDismissRequest = { showSimulationTypeDropdown = false }
                                ) {
                                    simulationTypes.forEach { type ->
                                        DropdownMenuItem(
                                            text = {
                                                Text(
                                                    text = type,
                                                    style = MaterialTheme.typography.bodyMedium,
                                                    fontWeight = if (type == simulationType)
                                                        FontWeight.Bold
                                                    else
                                                        FontWeight.Normal
                                                )
                                            },
                                            onClick = {
                                                simulationType = type
                                                showSimulationTypeDropdown = false
                                            },
                                            colors = MenuDefaults.itemColors(
                                                textColor = MaterialTheme.colorScheme.onSurface,
                                                leadingIconColor = MaterialTheme.colorScheme.primary
                                            )
                                        )
                                    }
                                }
                            }

                            // Settings Button with improved styling
                            IconButton(
                                onClick = { showSettingsDialog = true },
                                enabled = !isMonitoring,
                                modifier = Modifier
                                    .padding(start = 8.dp)
                                    .size(40.dp)
                                    .background(
                                        color = if (!isMonitoring)
                                            MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                                        else
                                            MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f),
                                        shape = CircleShape
                                    )
                            ) {
                                Icon(
                                    Icons.Filled.Settings,
                                    "Configure Monitoring Mode",
                                    tint = if (!isMonitoring)
                                        MaterialTheme.colorScheme.primary
                                    else
                                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f),
                                    modifier = Modifier.size(24.dp)
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(16.dp))

                        // Display Current Mode with improved styling
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Current Mode:",
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Medium,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            // Mode indicator with pill shape background
                            Box(
                                modifier = Modifier
                                    .background(
                                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.15f),
                                        shape = RoundedCornerShape(16.dp)
                                    )
                                    .padding(horizontal = 12.dp, vertical = 4.dp)
                            ) {
                                Text(
                                    text = monitoringMode.name.replace("_", " "),
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    }
                } // End Configuration Card Column

                // --- Mannequin/Scenario Configuration (Simulation Mode Only) ---
                if (monitoringMode == MonitoringMode.SIMULATION) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp)
                            .height(250.dp), // Reduced height to fit better on screen
                        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Column(
                            modifier = Modifier
                                .padding(horizontal = 16.dp, vertical = 12.dp)
                                .fillMaxSize()
                        ) {
                            Text(
                                text = "Mannequin Simulation Configuration",
                                style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                            HorizontalDivider(
                                modifier = Modifier.padding(vertical = 8.dp),
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.2f)
                            )

                            if (selectedMannequins.isEmpty()) {
                                Box(
                                    modifier = Modifier
                                        .height(150.dp) // Reduced height to fit better
                                        .fillMaxWidth(),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "No mannequins added. Click '+' to add.",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                                        textAlign = TextAlign.Center
                                    )
                                }
                            } else {
                                // Use a fixed height Column with scroll
                                Column(
                                    modifier = Modifier
                                        .height(150.dp) // Reduced height to fit better
                                        .fillMaxWidth()
                                        .verticalScroll(rememberScrollState())
                                ) {
                                    selectedMannequins.forEach { mannequin ->
                                        MannequinScenarioItem(
                                            mannequin = mannequin,
                                            scenario = mannequinScenarios[mannequin] ?: availableScenarios.first(),
                                            availableScenarios = availableScenarios,
                                            onScenarioChanged = { newScenario ->
                                                if (!isMonitoring) { // Prevent changes while monitoring
                                                    mannequinScenarios = mannequinScenarios.toMutableMap().apply { put(mannequin, newScenario) }
                                                }
                                            },
                                            onRemove = {
                                                if (!isMonitoring) { // Prevent changes while monitoring
                                                    selectedMannequins = selectedMannequins - mannequin
                                                    mannequinScenarios = mannequinScenarios - mannequin
                                                }
                                            },
                                            enabled = !isMonitoring
                                        )

                                        // Add spacer between items
                                        if (mannequin != selectedMannequins.last()) {
                                            Spacer(modifier = Modifier.height(8.dp))
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    // Placeholder or Info for Live Mode Configuration Section
                    Spacer(modifier = Modifier.weight(1f)) // Use spacer to maintain layout balance
                }


                // --- Live Monitoring Display Area (If Monitoring Active) ---
                if (isMonitoring) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(300.dp), // Fixed height instead of weight
                        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
                    ) {
                        Column(modifier = Modifier.padding(16.dp).fillMaxSize()) {
                            Text("Live Monitoring", style = MaterialTheme.typography.titleMedium)
                            HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp))
                            Column(Modifier.weight(1f).verticalScroll(rememberScrollState())) {
                                val mannequinsToDisplay = if (monitoringMode == MonitoringMode.SIMULATION) {
                                    selectedMannequins
                                } else { // LIVE_ZOLL mode
                                    // Display based on actual data received from SDK-discovered devices
                                    latestVitalSignsByMannequin.keys.toList()
                                }
                                if (mannequinsToDisplay.isEmpty()){
                                     Box(Modifier.fillMaxSize(), contentAlignment = Alignment.Center){
                                         Text("Monitoring started, waiting for data...")
                                     }
                                } else {
                                    // Display active non-compliance events at the top
                                    if (activeNonComplianceEvents.isNotEmpty()) {
                                        Card(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(bottom = 8.dp),
                                            colors = CardDefaults.cardColors(
                                                containerColor = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.2f)
                                            )
                                        ) {
                                            Column(modifier = Modifier.padding(8.dp)) {
                                                Text(
                                                    text = "Active Non-Compliance Events",
                                                    style = MaterialTheme.typography.titleMedium,
                                                    fontWeight = FontWeight.Bold
                                                )

                                                Spacer(modifier = Modifier.height(4.dp))

                                                activeNonComplianceEvents.values.forEach { event ->
                                                    ActiveNonComplianceIndicator(
                                                        event = event,
                                                        onClick = { activeEvent ->
                                                            // Add annotation for this event
                                                            mannequinRepository.addAnnotation(
                                                                Annotation(
                                                                    text = "Non-compliant ${activeEvent.vitalSign}: ${activeEvent.currentValue.toInt()}",
                                                                    type = AnnotationType.CRITICAL_ALERT,
                                                                    vitalSign = activeEvent.vitalSign,
                                                                    mannequin = activeEvent.mannequin,
                                                                    relatedNonComplianceEventId = activeEvent.id
                                                                )
                                                            )
                                                        }
                                                    )
                                                }

                                                // Add timeline of non-compliance events
                                                if (nonComplianceEvents.isNotEmpty() && sessionStartTime != null) {
                                                    Spacer(modifier = Modifier.height(8.dp))
                                                    Text(
                                                        text = "Non-Compliance Timeline",
                                                        style = MaterialTheme.typography.titleSmall
                                                    )
                                                    Spacer(modifier = Modifier.height(4.dp))
                                                    NonComplianceTimeline(
                                                        nonComplianceEvents = nonComplianceEvents,
                                                        sessionStartTime = sessionStartTime!!,
                                                        sessionEndTime = Date()
                                                    )
                                                }
                                            }
                                        }
                                    }

                                    // Display mannequin monitor cards
                                    mannequinsToDisplay.forEach { deviceId -> // Renamed to deviceId for clarity in LIVE_ZOLL mode
                                        val config = if (monitoringMode == MonitoringMode.LIVE_ZOLL) {
                                            liveMonitorConfigs.find { it.deviceSerial == deviceId }
                                        } else null

                                        val mannequinName = if (monitoringMode == MonitoringMode.SIMULATION) deviceId
                                                          else config?.mannequinName ?: deviceId

                                        MannequinMonitorCard(
                                            mannequinName = mannequinName,
                                            scenario = if (monitoringMode == MonitoringMode.SIMULATION) mannequinScenarios[deviceId] ?: "Unknown"
                                                       else config?.scenarioName ?: "Live Data", // Use scenario from config or generic placeholder
                                            vitalSign = latestVitalSignsByMannequin[deviceId],
                                            connectionStatus = connectionStatuses[deviceId] ?: ConnectionStatus.DISCONNECTED
                                        )

                                        // Show annotations for this mannequin
                                        val mannequinAnnotations = annotations.filter { it.mannequin == mannequinName }
                                        if (mannequinAnnotations.isNotEmpty()) {
                                            Card(
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    .padding(start = 16.dp, end = 8.dp, bottom = 8.dp),
                                                colors = CardDefaults.cardColors(
                                                    containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                                                )
                                            ) {
                                                Column(modifier = Modifier.padding(8.dp)) {
                                                    Text(
                                                        text = "Annotations for $mannequinName",
                                                        style = MaterialTheme.typography.bodyMedium,
                                                        fontWeight = FontWeight.Bold
                                                    )

                                                    mannequinAnnotations.take(3).forEach { annotation ->
                                                        Text(
                                                            text = "• ${annotation.text}",
                                                            style = MaterialTheme.typography.bodySmall
                                                        )
                                                    }

                                                    if (mannequinAnnotations.size > 3) {
                                                        Text(
                                                            text = "... and ${mannequinAnnotations.size - 3} more",
                                                            style = MaterialTheme.typography.bodySmall,
                                                            color = MaterialTheme.colorScheme.primary
                                                        )
                                                    }
                                                }
                                            }
                                        }

                                        Spacer(Modifier.height(8.dp)) // Add spacing between cards
                                    }

                                    // Add annotation input at the bottom
                                    AnnotationInput(
                                        onAddAnnotation = { text, type ->
                                            mannequinRepository.addAnnotation(
                                                Annotation(
                                                    text = text,
                                                    type = type
                                                )
                                            )
                                        }
                                    )

                                    // Button to show all annotations
                                    if (annotations.isNotEmpty()) {
                                        TextButton(
                                            onClick = { showAnnotationsDialog = true },
                                            modifier = Modifier.align(Alignment.End)
                                        ) {
                                            Text("View All Annotations (${annotations.size})")
                                        }
                                    }
                                }
                            }
                            // Session Info Row
                             Row(
                                modifier = Modifier.fillMaxWidth().padding(top = 8.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // Session Timer
                                 sessionStartTime?.let { startTime ->
                                     val durationMillis = System.currentTimeMillis() - startTime.time
                                     val minutes = (durationMillis / 1000) / 60
                                     val seconds = (durationMillis / 1000) % 60
                                     Text("Time: ${String.format("%02d:%02d", minutes, seconds)}")
                                 } ?: Text("Time: 00:00")

                                // Session Type Info
                                Text("Type: $simulationType", fontWeight = FontWeight.Bold)
                            }
                        }
                    }
                } else {
                     // Placeholder when not monitoring (takes up space of monitoring card)
                     Spacer(modifier = Modifier.height(50.dp)) // Fixed height spacer
                 }


                // --- Action Button Row with professional medical styling ---
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 24.dp, bottom = 24.dp) // Increased padding for better spacing
                ) {
                    // Main action button with improved styling
                    Button(
                        onClick = {
                            if (isMonitoring) stopMonitoring() else startMonitoring()
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (isMonitoring)
                                MaterialTheme.colorScheme.error // Use theme error color for stop
                            else
                                MaterialTheme.colorScheme.primary, // Use theme primary for start
                            contentColor = Color.White,
                            disabledContainerColor = MaterialTheme.colorScheme.surfaceVariant,
                            disabledContentColor = MaterialTheme.colorScheme.onSurfaceVariant
                        ),
                        enabled = initialSettingsLoaded, // Only enable after settings load
                        modifier = Modifier
                            .align(Alignment.Center)
                            .fillMaxWidth(0.85f) // Slightly wider for better appearance
                            .height(60.dp), // Slightly taller for better touch target
                        shape = RoundedCornerShape(30.dp), // More rounded corners for a modern medical look
                        elevation = ButtonDefaults.buttonElevation(
                            defaultElevation = 4.dp, // Slightly more elevation for emphasis
                            pressedElevation = 8.dp
                        )
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center
                        ) {
                            // Icon with background for more professional appearance
                            Box(
                                modifier = Modifier
                                    .size(36.dp)
                                    .background(
                                        color = Color.White.copy(alpha = 0.2f),
                                        shape = CircleShape
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = if (isMonitoring) Icons.Default.Stop else Icons.Default.PlayArrow,
                                    contentDescription = if (isMonitoring) "Stop Monitoring" else "Start Monitoring",
                                    modifier = Modifier.size(24.dp),
                                    tint = Color.White
                                )
                            }

                            Spacer(modifier = Modifier.width(16.dp))

                            Text(
                                text = if (isMonitoring) "STOP MONITORING" else "START MONITORING",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold,
                                letterSpacing = 0.5.sp // Slightly increased letter spacing for emphasis
                            )
                        }
                    }

                    // Status indicator when loading
                    if (!initialSettingsLoaded) {
                        Box(
                            modifier = Modifier
                                .align(Alignment.Center)
                                .fillMaxWidth(0.85f)
                                .height(60.dp)
                                .background(
                                    color = MaterialTheme.colorScheme.surfaceVariant,
                                    shape = RoundedCornerShape(30.dp)
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center
                            ) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(24.dp),
                                    color = MaterialTheme.colorScheme.primary,
                                    strokeWidth = 2.dp
                                )
                                Spacer(modifier = Modifier.width(16.dp))
                                Text(
                                    text = "INITIALIZING...",
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Medium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                }

            } // End Main Column
        } // End Else for Loaded State

        // --- Dialogs --- (Placed outside the main content Column but within Scaffold scope)
        if (showAddMannequinDialog && monitoringMode == MonitoringMode.SIMULATION) {
             var mannequinToAdd by remember { mutableStateOf(availableMannequins.firstOrNull { it !in selectedMannequins } ?: "") }
             var showMannequinDropdown by remember { mutableStateOf(false) }

             AlertDialog(
                 onDismissRequest = { showAddMannequinDialog = false },
                 title = { Text("Add Mannequin") },
                 text = {
                     Column {
                         Text("Select a mannequin to monitor:")
                         Spacer(modifier = Modifier.height(8.dp))

                         Box {
                             OutlinedTextField(
                                 value = mannequinToAdd.ifEmpty { "Select Mannequin..." }, // Placeholder if empty
                                 onValueChange = {}, readOnly = true,
                                 modifier = Modifier.fillMaxWidth(),
                                 trailingIcon = { IconButton(onClick = { showMannequinDropdown = true }) { Icon(Icons.Default.ArrowDropDown, "Select Mannequin") } }
                             )

                             DropdownMenu(
                                 expanded = showMannequinDropdown,
                                 onDismissRequest = { showMannequinDropdown = false }
                             ) {
                                 val mannequinsToShow = availableMannequins.filter { it !in selectedMannequins }

                                 if (mannequinsToShow.isEmpty()) {
                                     DropdownMenuItem(
                                         text = { Text("All mannequins added") },
                                         onClick = { showMannequinDropdown = false },
                                         enabled = false
                                     )
                                 } else {
                                     mannequinsToShow.forEach { mannequin ->
                                         DropdownMenuItem(text = { Text(mannequin) }, onClick = {
                                             mannequinToAdd = mannequin
                                             showMannequinDropdown = false
                                         })
                                     }
                                 }
                             }
                         }
                     }
                 },
                 confirmButton = {
                     Button(
                         onClick = {
                             if (mannequinToAdd.isNotEmpty() && mannequinToAdd !in selectedMannequins) {
                                 selectedMannequins = selectedMannequins + mannequinToAdd
                                 mannequinScenarios = mannequinScenarios.toMutableMap().apply {
                                     put(mannequinToAdd, availableScenarios.firstOrNull() ?: "Default Scenario")
                                 }
                             }
                             showAddMannequinDialog = false
                         },
                         enabled = mannequinToAdd.isNotEmpty() && mannequinToAdd !in selectedMannequins && availableMannequins.contains(mannequinToAdd)
                     ) { Text("Add") }
                 },
                 dismissButton = {
                     TextButton(onClick = { showAddMannequinDialog = false }) { Text("Cancel") }
                 }
             )
         }

         if (showSettingsDialog) {
             MonitorSettingsDialog(
                 currentMode = monitoringMode,
                 currentConfigs = liveMonitorConfigs,
                 availableMannequins = availableMannequins,
                 availableSimulations = Constants.SIM_MANNEQUINS.keys.toList(),
                 availableScenarios = ClinicalPracticeGuidelines.ALL_THRESHOLDS.keys.toList(),
                 onDismiss = { showSettingsDialog = false },
                 onSave = { newMode, newConfigs ->
                     monitoringMode = newMode
                     liveMonitorConfigs = newConfigs
                     showSettingsDialog = false
                     scope.launch {
                         try {
                             mannequinRepository.saveMonitoringMode(newMode)
                             mannequinRepository.saveLiveMonitorConfigs(newConfigs)
                             Toast.makeText(context, "Settings saved", Toast.LENGTH_SHORT).show()
                         } catch (e: Exception) {
                             Log.e("RealTimeMonitorScreen", "Error saving settings", e)
                             Toast.makeText(context, "Error saving settings", Toast.LENGTH_SHORT).show()
                         }
                     }
                 }
             )
         }

         if (showSaveDialog) {
             Log.d("RealTimeMonitorScreen", "Save dialog is showing")
             AlertDialog(
                onDismissRequest = {
                    Log.d("RealTimeMonitorScreen", "Save dialog dismissed")
                    showSaveDialog = false
                },
                title = { Text("Session Ended")},
                text = { Text("Session stopped. Save data to database?")},
                confirmButton = {
                    Button(onClick = {
                        Log.d("RealTimeMonitorScreen", "Save button clicked")
                        scope.launch {
                            try {
                                val recordsSaved = withContext(Dispatchers.IO) {
                                    mannequinRepository.saveCurrentDataToDatabase(
                                        sessionMetadata = SessionMetadata(
                                            type = simulationType,
                                            name = "Real-Time Session",
                                            runNumber = "1",
                                            startTime = sessionStartTime ?: Date(),
                                            endTime = Date()
                                        )
                                    )
                                }
                                if (recordsSaved > 0) {
                                    Toast.makeText(context, "Data saved successfully ($recordsSaved records)", Toast.LENGTH_SHORT).show()
                                } else {
                                    Toast.makeText(context, "No data to save", Toast.LENGTH_SHORT).show()
                                }
                            } catch (e: Exception) {
                                Log.e("RealTimeMonitorScreen", "Error saving data", e)
                                Toast.makeText(context, "Error saving data: ${e.message}", Toast.LENGTH_SHORT).show()
                            }
                        }
                        showSaveDialog = false
                    }) {
                        Text("Save")
                    }
                },
                dismissButton = { TextButton(onClick = { showSaveDialog = false }) { Text("Discard") } }
             )
         }

         // Annotations Dialog
         if (showAnnotationsDialog) {
             AlertDialog(
                onDismissRequest = { showAnnotationsDialog = false },
                title = { Text("All Annotations") },
                text = {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .heightIn(max = 400.dp)
                    ) {
                        if (annotations.isEmpty()) {
                            Text("No annotations yet.")
                        } else {
                            AnnotationsList(
                                annotations = annotations,
                                onDeleteAnnotation = { annotationId ->
                                    mannequinRepository.deleteAnnotation(annotationId)
                                }
                            )
                        }
                    }
                },
                confirmButton = {
                    Button(onClick = { showAnnotationsDialog = false }) {
                        Text("Close")
                    }
                }
             )
         }

    } // End Scaffold Content Lambda
}

// --- Implementation for MonitorSettingsDialog ---
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MonitorSettingsDialog(
    currentMode: MonitoringMode,
    currentConfigs: List<LiveSessionMannequinConfig>, // UPDATED TYPE
    availableMannequins: List<String>, // Full list of potential mannequins
    availableSimulations: List<String>, // New parameter
    availableScenarios: List<String>,   // New parameter
    onDismiss: () -> Unit,
    onSave: (MonitoringMode, List<LiveSessionMannequinConfig>) -> Unit // UPDATED TYPE
) {
    // Internal state for the dialog
    var selectedMode by remember { mutableStateOf(currentMode) }
    // Keep track of configs being edited in the dialog
    var tempLiveConfigs by remember { mutableStateOf(currentConfigs.toMutableList()) }

    // Derived state: mannequins already assigned in tempLiveConfigs
    val assignedMannequins by remember {
        derivedStateOf { tempLiveConfigs.map { it.mannequinName }.toSet() }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Monitor Settings") },
        text = {
            Column(
                modifier = Modifier.verticalScroll(rememberScrollState()) // Make the dialog content scrollable
            ) {
                // --- Mode Selection ---
                Text("Select Monitoring Mode:", style = MaterialTheme.typography.titleMedium)
                Row(verticalAlignment = Alignment.CenterVertically) {
                    RadioButton(
                        selected = selectedMode == MonitoringMode.SIMULATION,
                        onClick = { selectedMode = MonitoringMode.SIMULATION }
                    )
                    Text("Simulation Mode")
                }
                Row(verticalAlignment = Alignment.CenterVertically) {
                    RadioButton(
                        selected = selectedMode == MonitoringMode.LIVE_ZOLL,
                        onClick = { selectedMode = MonitoringMode.LIVE_ZOLL }
                    )
                    Text("Live Zoll Monitor Mode")
                }

                Spacer(modifier = Modifier.height(16.dp))

                // --- Live Monitor Configuration (Conditional) ---
                if (selectedMode == MonitoringMode.LIVE_ZOLL) {
                    HorizontalDivider()
                    Spacer(modifier = Modifier.height(8.dp))
                    Text("Live Monitor Configuration (Max 5):", style = MaterialTheme.typography.titleMedium)
                    Spacer(modifier = Modifier.height(8.dp))

                    if (tempLiveConfigs.isEmpty()) {
                        Text("No live monitors configured. Click 'Add Monitor'.")
                    }

                    // Use Column for fixed items + Add button
                    Column {
                        tempLiveConfigs.forEachIndexed { index, config ->
                            LiveMonitorConfigRow(
                                config = config,
                                availableMannequins = availableMannequins,
                                assignedMannequins = assignedMannequins,
                                availableSimulations = availableSimulations,
                                availableScenarios = availableScenarios,
                                onConfigChange = { updatedConfig ->
                                    tempLiveConfigs = tempLiveConfigs.toMutableList().also {
                                        it[index] = updatedConfig
                                    }
                                },
                                onRemove = {
                                    tempLiveConfigs = tempLiveConfigs.toMutableList().also {
                                        it.removeAt(index)
                                    }
                                }
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                        }

                        // Add New Monitor Button
                        if (tempLiveConfigs.size < 5) {
                            OutlinedButton(
                                onClick = {
                                    tempLiveConfigs = tempLiveConfigs.toMutableList().also {
                                        // Add a new blank config entry
                                        val nextAvailableMannequin = availableMannequins.firstOrNull { it !in assignedMannequins } ?: ""
                                        // Create a default LiveSessionMannequinConfig
                                        it.add(LiveSessionMannequinConfig(
                                            deviceSerial = "", // User to fill
                                            mannequinName = nextAvailableMannequin,
                                            simulationName = availableSimulations.firstOrNull() ?: "",
                                            scenarioName = availableScenarios.firstOrNull() ?: "",
                                            simulationType = "Training" // Default type
                                        ))
                                    }
                                },
                                modifier = Modifier.align(Alignment.End)
                            ) {
                                Icon(Icons.Default.Add, contentDescription = "Add Monitor")
                                Spacer(modifier = Modifier.width(4.dp))
                                Text("Add Monitor")
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            Button(onClick = {
                // Validate IP addresses before saving?
                // For now, just save
                onSave(selectedMode, tempLiveConfigs.toList()) // Save the internal state
            }) {
                Text("Save")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

// --- Composable for a single Live Monitor Config Row ---
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LiveMonitorConfigRow(
    config: LiveSessionMannequinConfig, // UPDATED TYPE
    availableMannequins: List<String>,
    assignedMannequins: Set<String>, // Mannequins assigned in *other* rows
    availableSimulations: List<String>, // New parameter
    availableScenarios: List<String>,   // New parameter
    onConfigChange: (LiveSessionMannequinConfig) -> Unit, // UPDATED TYPE
    onRemove: () -> Unit
) {
    var deviceSerialState by remember { mutableStateOf(config.deviceSerial) }
    var selectedMannequinState by remember { mutableStateOf(config.mannequinName) }
    var selectedSimulationState by remember { mutableStateOf(config.simulationName) }
    var selectedScenarioState by remember { mutableStateOf(config.scenarioName) }
    var selectedSimulationTypeState by remember { mutableStateOf(config.simulationType) }

    var isMannequinDropdownExpanded by remember { mutableStateOf(false) }
    var isSimulationDropdownExpanded by remember { mutableStateOf(false) }
    var isScenarioDropdownExpanded by remember { mutableStateOf(false) }
    var isSimTypeDropdownExpanded by remember { mutableStateOf(false) }

    val simulationTypes = listOf("Training", "Graded")

    // Mannequins available for *this* dropdown
    val mannequinsForThisRow = remember(availableMannequins, assignedMannequins, config.mannequinName) {
        availableMannequins.filter { it == config.mannequinName || it !in assignedMannequins }
    }

    // Scenarios: If a simulation is selected, filter scenarios; otherwise, show all.
    // This requires Constants.SIM_MANNEQUIN_SCENARIO to be structured for easy lookup,
    // or a more complex filtering logic. For now, we use all availableScenarios.
    // Later, we can enhance this if Constants.SIM_SCENARIOS mapping is easily usable.
    val scenariosForThisRow = availableScenarios // Using all scenarios for now

    Column(modifier = Modifier.padding(vertical = 4.dp)) { // Each config row is now a Column
        Row( // First row for Mannequin and Device Serial
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Mannequin Selector Dropdown
            Box(modifier = Modifier.weight(1f)) { // Adjusted weight
                ExposedDropdownMenuBox(
                    expanded = isMannequinDropdownExpanded,
                    onExpandedChange = { isMannequinDropdownExpanded = it }
                ) {
                    OutlinedTextField(
                        value = selectedMannequinState.ifEmpty { "Select Mannequin" },
                        onValueChange = { /* Read Only */ },
                        readOnly = true,
                        label = { Text("Mannequin") },
                        modifier = Modifier.menuAnchor().fillMaxWidth(),
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isMannequinDropdownExpanded) }
                    )
                    DropdownMenu(
                        expanded = isMannequinDropdownExpanded,
                        onDismissRequest = { isMannequinDropdownExpanded = false }
                    ) {
                        if (mannequinsForThisRow.isEmpty()) {
                            DropdownMenuItem(
                                text = { Text("No available mannequins") },
                                onClick = { isMannequinDropdownExpanded = false },
                                enabled = false
                            )
                        } else {
                            mannequinsForThisRow.forEach { mannequin ->
                                DropdownMenuItem(
                                    text = { Text(mannequin) },
                                    onClick = {
                                        selectedMannequinState = mannequin
                                        isMannequinDropdownExpanded = false
                                        onConfigChange(config.copy(
                                            mannequinName = mannequin,
                                            deviceSerial = deviceSerialState,
                                            simulationName = selectedSimulationState,
                                            scenarioName = selectedScenarioState,
                                            simulationType = selectedSimulationTypeState
                                        ))
                                    }
                                )
                            }
                        }
                    }
                }
            }

            // Device Serial Input
            OutlinedTextField(
                value = deviceSerialState,
                onValueChange = {
                    deviceSerialState = it
                    onConfigChange(config.copy(
                        deviceSerial = it,
                        mannequinName = selectedMannequinState,
                        simulationName = selectedSimulationState,
                        scenarioName = selectedScenarioState,
                        simulationType = selectedSimulationTypeState
                    ))
                },
                label = { Text("Device Serial") },
                singleLine = true,
                modifier = Modifier.weight(1f) // Adjusted weight
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Row( // Second row for Simulation and Scenario
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Simulation Name Dropdown
            Box(modifier = Modifier.weight(1f)) {
                ExposedDropdownMenuBox(
                    expanded = isSimulationDropdownExpanded,
                    onExpandedChange = { isSimulationDropdownExpanded = it }
                ) {
                    OutlinedTextField(
                        value = selectedSimulationState.ifEmpty { "Select Simulation" },
                        onValueChange = { /* Read Only */ },
                        readOnly = true,
                        label = { Text("Simulation") },
                        modifier = Modifier.menuAnchor().fillMaxWidth(),
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isSimulationDropdownExpanded) }
                    )
                    DropdownMenu(
                        expanded = isSimulationDropdownExpanded,
                        onDismissRequest = { isSimulationDropdownExpanded = false }
                    ) {
                        availableSimulations.forEach { sim ->
                            DropdownMenuItem(
                                text = { Text(sim) },
                                onClick = {
                                    selectedSimulationState = sim
                                    isSimulationDropdownExpanded = false
                                    // Potentially auto-select scenario based on sim and mannequin here
                                    // For now, user selects scenario independently
                                    onConfigChange(config.copy(
                                        simulationName = sim,
                                        mannequinName = selectedMannequinState,
                                        deviceSerial = deviceSerialState,
                                        scenarioName = selectedScenarioState, // Keep current or reset?
                                        simulationType = selectedSimulationTypeState
                                    ))
                                }
                            )
                        }
                    }
                }
            }

            // Scenario Name Dropdown
            Box(modifier = Modifier.weight(1f)) {
                ExposedDropdownMenuBox(
                    expanded = isScenarioDropdownExpanded,
                    onExpandedChange = { isScenarioDropdownExpanded = it }
                ) {
                    OutlinedTextField(
                        value = selectedScenarioState.ifEmpty { "Select Scenario" },
                        onValueChange = { /* Read Only */ },
                        readOnly = true,
                        label = { Text("Scenario") },
                        modifier = Modifier.menuAnchor().fillMaxWidth(),
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isScenarioDropdownExpanded) }
                    )
                    DropdownMenu(
                        expanded = isScenarioDropdownExpanded,
                        onDismissRequest = { isScenarioDropdownExpanded = false }
                    ) {
                        scenariosForThisRow.forEach { scenario -> // Use filtered scenarios if logic is added
                            DropdownMenuItem(
                                text = { Text(scenario) },
                                onClick = {
                                    selectedScenarioState = scenario
                                    isScenarioDropdownExpanded = false
                                    onConfigChange(config.copy(
                                        scenarioName = scenario,
                                        mannequinName = selectedMannequinState,
                                        deviceSerial = deviceSerialState,
                                        simulationName = selectedSimulationState,
                                        simulationType = selectedSimulationTypeState
                                    ))
                                }
                            )
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        Row( // Third row for Simulation Type and Remove button
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween // Pushes remove button to the end
        ) {
            // Simulation Type Dropdown
            Box(modifier = Modifier.weight(1f)) { // Takes available space
                 ExposedDropdownMenuBox(
                    expanded = isSimTypeDropdownExpanded,
                    onExpandedChange = { isSimTypeDropdownExpanded = it }
                ) {
                    OutlinedTextField(
                        value = selectedSimulationTypeState,
                        onValueChange = { /* Read Only */ },
                        readOnly = true,
                        label = { Text("Session Type") },
                        modifier = Modifier.menuAnchor().fillMaxWidth(),
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isSimTypeDropdownExpanded) }
                    )
                    DropdownMenu(
                        expanded = isSimTypeDropdownExpanded,
                        onDismissRequest = { isSimTypeDropdownExpanded = false }
                    ) {
                        simulationTypes.forEach { type ->
                            DropdownMenuItem(
                                text = { Text(type) },
                                onClick = {
                                    selectedSimulationTypeState = type
                                    isSimTypeDropdownExpanded = false
                                    onConfigChange(config.copy(
                                        simulationType = type,
                                        mannequinName = selectedMannequinState,
                                        deviceSerial = deviceSerialState,
                                        simulationName = selectedSimulationState,
                                        scenarioName = selectedScenarioState
                                    ))
                                }
                            )
                        }
                    }
                }
            }

            Spacer(Modifier.width(8.dp)) // Space before remove button

            // Remove Button
            IconButton(onClick = onRemove) {
                Icon(Icons.Default.Delete, contentDescription = "Remove Monitor Config", tint = MaterialTheme.colorScheme.error)
            }
        }
        HorizontalDivider(modifier = Modifier.padding(top = 8.dp)) // Divider after each config item
    }
}

/**
 * Display for a vital sign with a single value
 */
@Composable
fun VitalSignDisplay(
    name: String,
    value: Double?,
    unit: String,
    color: Color,
    status: String?,
    modifier: Modifier = Modifier
) {
    val vitalSignStatus = MonitorReadingStatus.fromString(status)
    val isValid = vitalSignStatus == MonitorReadingStatus.VALID

    Surface(
        shape = RoundedCornerShape(8.dp),
        color = color.copy(alpha = 0.1f),
        modifier = modifier
    ) {
        Column(
            modifier = Modifier.padding(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Name
            Text(
                text = name,
                style = MaterialTheme.typography.labelMedium,
                color = color,
                fontWeight = FontWeight.Bold
            )

            // Value
            if (isValid && value != null) {
                Text(
                    text = String.format("%.0f", value),
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
            } else {
                Text(
                    text = "---",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
            }

            // Unit
            Text(
                text = unit,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )

            // Status icon if not valid
            if (!isValid) {
                Icon(
                    imageVector = Icons.Filled.Warning,
                    contentDescription = "Invalid data",
                    tint = Danger,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

/**
 * Display for a vital sign with primary and secondary values (e.g., systolic/diastolic BP)
 */
@Composable
fun VitalSignDisplayWithSecondary(
    name: String,
    primaryValue: Double?,
    secondaryValue: Double?,
    unit: String,
    color: Color,
    status: String?,
    modifier: Modifier = Modifier
) {
    val vitalSignStatus = MonitorReadingStatus.fromString(status)
    val isValid = vitalSignStatus == MonitorReadingStatus.VALID

    Surface(
        shape = RoundedCornerShape(8.dp),
        color = color.copy(alpha = 0.1f),
        modifier = modifier
    ) {
        Column(
            modifier = Modifier.padding(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Name
            Text(
                text = name,
                style = MaterialTheme.typography.labelMedium,
                color = color,
                fontWeight = FontWeight.Bold
            )

            // Value
            if (isValid && primaryValue != null && secondaryValue != null) {
                Text(
                    text = buildAnnotatedString {
                        append(String.format("%.0f", primaryValue))
                        append("/")
                        append(String.format("%.0f", secondaryValue))
                    },
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
            } else {
                Text(
                    text = "---/---",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
            }

            // Unit
            Text(
                text = unit,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )

            // Status icon if not valid
            if (!isValid) {
                Icon(
                    imageVector = Icons.Filled.Warning,
                    contentDescription = "Invalid data",
                    tint = Danger,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

/**
 * Calculates the duration of a session in a friendly format
 */
private fun calculateSessionDuration(startTime: Date?): String {
    if (startTime == null) return "Unknown"

    val durationMillis = Date().time - startTime.time
    val seconds = durationMillis / 1000
    val minutes = seconds / 60
    val hours = minutes / 60

    return when {
        hours > 0 -> "$hours hours, ${minutes % 60} minutes"
        minutes > 0 -> "$minutes minutes, ${seconds % 60} seconds"
        else -> "$seconds seconds"
    }
}

/**
 * Composable to display a mannequin and its scenario with editing options
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MannequinScenarioItem(
    mannequin: String,
    scenario: String,
    availableScenarios: List<String>,
    onScenarioChanged: (String) -> Unit,
    onRemove: () -> Unit,
    enabled: Boolean = true
) {
    var showScenarioDropdown by remember { mutableStateOf(false) }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Mannequin name
        Column(modifier = Modifier.weight(0.3f)) {
            Text(
                text = "Mannequin",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = mannequin,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
        }

        // Scenario selection
        Column(modifier = Modifier.weight(0.7f)) {
            Text(
                text = "Scenario",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            ExposedDropdownMenuBox(
                expanded = showScenarioDropdown,
                onExpandedChange = {
                    if (enabled) {
                        showScenarioDropdown = it
                    }
                }
            ) {
                OutlinedTextField(
                    value = scenario,
                    onValueChange = {},
                    readOnly = true,
                    enabled = enabled,
                    modifier = Modifier
                        .fillMaxWidth()
                        .menuAnchor(),
                    trailingIcon = {
                        if (enabled) {
                            Row {
                                ExposedDropdownMenuDefaults.TrailingIcon(
                                    expanded = showScenarioDropdown
                                )
                                // Remove button
                                IconButton(
                                    onClick = onRemove,
                                    enabled = enabled // Control remove button enabled state
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Close,
                                        contentDescription = "Remove Mannequin", // More specific description
                                        tint = if (enabled) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f) // Dim if disabled
                                    )
                                }
                            }
                        }
                    }
                )

                DropdownMenu(
                    expanded = showScenarioDropdown,
                    onDismissRequest = { showScenarioDropdown = false }
                ) {
                    availableScenarios.forEach { scenarioOption ->
                        DropdownMenuItem(
                            text = { Text(scenarioOption) },
                            onClick = {
                                onScenarioChanged(scenarioOption)
                                showScenarioDropdown = false
                            }
                        )
                    }
                }
            }
        }
    }
}