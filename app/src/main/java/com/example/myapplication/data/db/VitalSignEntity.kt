package com.example.myapplication.data.db

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.example.myapplication.data.model.VitalSign
import java.util.Date

/**
 * Database entity representing a vital sign record
 */
@Entity(tableName = "vital_signs")
@TypeConverters(Converters::class)
data class VitalSignEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val timeObj: Date,
    val timeStr: String,
    val deviceSerial: String?,
    val sourceFile: String?,

    // Vital signs
    val hr: Double?,
    val spO2: Double?,
    val nibpSys: Double?,
    val nibpDia: Double?,
    val nibpMap: Double?,
    val temp1: Double?,
    val respRate: Double?,
    val etCO2: Double?,
    val fiCO2: Double?,

    // SpO2-related parameters
    val spMet: Double?,
    val spCo: Double?,
    val pvi: Double?,
    val pi: Double?,
    val spOC: Double?,
    val spHb: Double?,

    // IBP readings
    val ibp1Sys: Double?,
    val ibp1Dia: Double?,
    val ibp1Map: Double?,

    // Status information
    val hrStatus: String?,
    val spO2Status: String?,
    val nibpSysStatus: String?,
    val nibpDiaStatus: String?,
    val nibpMapStatus: String?,
    val temp1Status: String?,
    val respRateStatus: String?,
    val etCO2Status: String?,

    // Simulation and timing data
    val course: String?,
    val sim: String?,
    val simType: String?,  // Added for storing session type (Training/Graded)
    val simRunNumber: String?, // Added for storing run number
    val rawMannequin: String?,
    val overrideMannequin: String?,
    val dateStr: String?,
    val scenario: String?,
    val inSimWindow: Boolean,
    val isValid: Boolean,
    val elapsedMin: Double?,
    val simulationType: String? // Added for explicit simulation type storage
) {
    /**
     * Convert entity to domain model
     */
    fun toVitalSign(): VitalSign {
        return VitalSign(
            timeObj = timeObj,
            timeStr = timeStr,
            deviceSerial = deviceSerial,
            sourceFile = sourceFile,
            hr = hr,
            spO2 = spO2,
            nibpSys = nibpSys,
            nibpDia = nibpDia,
            nibpMap = nibpMap,
            temp1 = temp1,
            respRate = respRate,
            etCO2 = etCO2,
            fiCO2 = fiCO2,
            spMet = spMet,
            spCo = spCo,
            pvi = pvi,
            pi = pi,
            spOC = spOC,
            spHb = spHb,
            ibp1Sys = ibp1Sys,
            ibp1Dia = ibp1Dia,
            ibp1Map = ibp1Map,
            hrStatus = hrStatus,
            spO2Status = spO2Status,
            nibpSysStatus = nibpSysStatus,
            nibpDiaStatus = nibpDiaStatus,
            nibpMapStatus = nibpMapStatus,
            temp1Status = temp1Status,
            respRateStatus = respRateStatus,
            etCO2Status = etCO2Status,
            course = course,
            sim = sim,
            simType = simType,
            simRunNumber = simRunNumber,
            rawMannequin = rawMannequin,
            overrideMannequin = overrideMannequin,
            dateStr = dateStr,
            scenario = scenario,
            inSimWindow = inSimWindow,
            isValid = isValid,
            elapsedMin = elapsedMin,
            simulationType = simulationType
        )
    }

    companion object {
        /**
         * Convert domain model to entity
         */
        fun fromVitalSign(vitalSign: VitalSign): VitalSignEntity {
            return VitalSignEntity(
                timeObj = vitalSign.timeObj,
                timeStr = vitalSign.timeStr,
                deviceSerial = vitalSign.deviceSerial,
                sourceFile = vitalSign.sourceFile,
                hr = vitalSign.hr,
                spO2 = vitalSign.spO2,
                nibpSys = vitalSign.nibpSys,
                nibpDia = vitalSign.nibpDia,
                nibpMap = vitalSign.nibpMap,
                temp1 = vitalSign.temp1,
                respRate = vitalSign.respRate,
                etCO2 = vitalSign.etCO2,
                fiCO2 = vitalSign.fiCO2,
                spMet = vitalSign.spMet,
                spCo = vitalSign.spCo,
                pvi = vitalSign.pvi,
                pi = vitalSign.pi,
                spOC = vitalSign.spOC,
                spHb = vitalSign.spHb,
                ibp1Sys = vitalSign.ibp1Sys,
                ibp1Dia = vitalSign.ibp1Dia,
                ibp1Map = vitalSign.ibp1Map,
                hrStatus = vitalSign.hrStatus,
                spO2Status = vitalSign.spO2Status,
                nibpSysStatus = vitalSign.nibpSysStatus,
                nibpDiaStatus = vitalSign.nibpDiaStatus,
                nibpMapStatus = vitalSign.nibpMapStatus,
                temp1Status = vitalSign.temp1Status,
                respRateStatus = vitalSign.respRateStatus,
                etCO2Status = vitalSign.etCO2Status,
                course = vitalSign.course,
                sim = vitalSign.sim,
                simType = vitalSign.simType,
                simRunNumber = vitalSign.simRunNumber,
                rawMannequin = vitalSign.rawMannequin,
                overrideMannequin = vitalSign.overrideMannequin,
                dateStr = vitalSign.dateStr,
                scenario = vitalSign.scenario,
                inSimWindow = vitalSign.inSimWindow,
                isValid = vitalSign.isValid,
                elapsedMin = vitalSign.elapsedMin,
                simulationType = vitalSign.simulationType
            )
        }
    }
}