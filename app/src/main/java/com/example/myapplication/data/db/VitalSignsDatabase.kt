package com.example.myapplication.data.db

import android.content.Context
import android.util.Log
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.example.myapplication.util.CrashLogger

/**
 * Room database implementation for vital signs
 */
@Database(entities = [VitalSignEntity::class], version = 2, exportSchema = false)
@TypeConverters(Converters::class)
abstract class VitalSignsDatabase : RoomDatabase() {
    /**
     * Get the DAO for vital sign operations
     */
    abstract fun vitalSignDao(): VitalSignDao

    companion object {
        @Volatile
        private var INSTANCE: VitalSignsDatabase? = null
        private const val TAG = "VitalSignsDatabase"

        /**
         * Migration from version 1 to 2
         * Adds simType, simRunNumber, and simulationType columns to vital_signs table
         */
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Add new columns to the vital_signs table
                database.execSQL("ALTER TABLE vital_signs ADD COLUMN simType TEXT")
                database.execSQL("ALTER TABLE vital_signs ADD COLUMN simRunNumber TEXT")
                database.execSQL("ALTER TABLE vital_signs ADD COLUMN simulationType TEXT")
            }
        }

        /**
         * Get database instance, creating it if necessary
         * @throws Exception if database creation fails
         */
        fun getDatabase(context: Context): VitalSignsDatabase {
            // if the INSTANCE is not null, then return it,
            // if it is, then create the database
            return INSTANCE ?: synchronized(this) {
                try {
                    val instance = Room.databaseBuilder(
                        context.applicationContext,
                        VitalSignsDatabase::class.java,
                        "vital_signs_database"
                    )
                        .addMigrations(MIGRATION_1_2) // Use our migration strategy
                        .build()
                    INSTANCE = instance
                    instance
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to create database instance", e)
                    CrashLogger.logError(TAG, "Failed to create database instance", e)
                    throw e
                }
            }
        }
    }
}